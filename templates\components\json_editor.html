<!-- JSON编辑器组件 -->
{% set editor_id = editor_id or 'jsonEditor' %}
{% set label = label or 'JSON内容' %}
{% set height = height or '400px' %}
{% set mode = mode or 'text' %}
{% set read_only = read_only or false %}
{% set show_validation = show_validation or true %}
{% set validation_id = validation_id or (editor_id + 'Validation') %}

<div class="json-editor-wrapper">
    <!-- 编辑器标签 -->
    {% if label %}
    <label for="{{ editor_id }}" class="form-label">
        <i class="fas fa-code me-1"></i>{{ label }}
    </label>
    {% endif %}
    
    <!-- 编辑器工具栏 -->
    <div class="json-editor-toolbar mb-2">
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-primary" onclick="formatJSON('{{ editor_id }}')">
                <i class="fas fa-indent me-1"></i>格式化
            </button>
            <button type="button" class="btn btn-outline-info" onclick="validateJSON('{{ editor_id }}')">
                <i class="fas fa-check-circle me-1"></i>验证
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="copyJSON('{{ editor_id }}')">
                <i class="fas fa-copy me-1"></i>复制
            </button>
        </div>
        
        <!-- 模式切换 -->
        <div class="btn-group btn-group-sm ms-2" role="group">
            <input type="radio" class="btn-check" name="{{ editor_id }}Mode" id="{{ editor_id }}ModeText" autocomplete="off" checked>
            <label class="btn btn-outline-secondary" for="{{ editor_id }}ModeText" onclick="setEditorMode('{{ editor_id }}', 'text')">
                <i class="fas fa-file-code me-1"></i>文本
            </label>
            
            <input type="radio" class="btn-check" name="{{ editor_id }}Mode" id="{{ editor_id }}ModeTree" autocomplete="off">
            <label class="btn btn-outline-secondary" for="{{ editor_id }}ModeTree" onclick="setEditorMode('{{ editor_id }}', 'tree')">
                <i class="fas fa-sitemap me-1"></i>树形
            </label>
        </div>
    </div>
    
    <!-- 编辑器容器 -->
    <div id="{{ editor_id }}" class="json-editor-container" style="height: {{ height }}; border: 1px solid #ddd; border-radius: 4px;"></div>
    
    <!-- 验证结果 -->
    {% if show_validation %}
    <div id="{{ validation_id }}" class="json-validation-result mt-2" style="display: none;">
        <!-- 验证结果将在这里显示 -->
    </div>
    {% endif %}
</div>

<!-- 样式 -->
<style>
.json-editor-wrapper {
    margin-bottom: 1rem;
}

.json-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.json-editor-container {
    background-color: #fff;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
}

.json-validation-result .alert {
    margin-bottom: 0;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .json-editor-container {
        background-color: #1e1e1e;
        border-color: #444;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .json-editor-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .json-editor-toolbar .btn-group {
        justify-content: center;
    }
}
</style>

<!-- JavaScript 初始化脚本 -->
<script>
// 存储编辑器实例
window.jsonEditors = window.jsonEditors || {};

// 初始化JSON编辑器
function initJSONEditor(editorId, options = {}) {
    const defaultOptions = {
        mode: '{{ mode }}',
        readOnly: {{ 'true' if read_only else 'false' }},
        mainMenuBar: false,
        navigationBar: false,
        statusBar: true,
        askToFormat: false,
        indentation: 2,
        tabSize: 2
    };
    
    const editorOptions = { ...defaultOptions, ...options };
    
    try {
        const editor = createJSONEditor(editorId, editorOptions);
        window.jsonEditors[editorId] = editor;
        
        // 添加变化监听器
        editor.on('change', function(updatedContent, previousContent, { contentErrors }) {
            if (contentErrors && contentErrors.length > 0) {
                showValidationErrors(editorId, contentErrors);
            } else {
                hideValidationErrors(editorId);
            }
        });
        
        return editor;
    } catch (error) {
        console.error('Failed to initialize JSON editor:', error);
        return null;
    }
}

// 格式化JSON
function formatJSON(editorId) {
    const editor = window.jsonEditors[editorId];
    if (editor) {
        try {
            editor.format();
            showNotification('JSON格式化成功', 'success');
        } catch (error) {
            showNotification('格式化失败: ' + error.message, 'error');
        }
    }
}

// 验证JSON
function validateJSON(editorId) {
    const editor = window.jsonEditors[editorId];
    if (editor) {
        const errors = editor.validate();
        if (errors.length === 0) {
            showValidationSuccess(editorId);
            showNotification('JSON验证通过', 'success');
        } else {
            showValidationErrors(editorId, errors);
            showNotification('JSON验证失败', 'error');
        }
    }
}

// 复制JSON
function copyJSON(editorId) {
    const editor = window.jsonEditors[editorId];
    if (editor) {
        try {
            const text = editor.getText();
            navigator.clipboard.writeText(text).then(() => {
                showNotification('JSON内容已复制到剪贴板', 'success');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('JSON内容已复制到剪贴板', 'success');
            });
        } catch (error) {
            showNotification('复制失败: ' + error.message, 'error');
        }
    }
}

// 设置编辑器模式
function setEditorMode(editorId, mode) {
    const editor = window.jsonEditors[editorId];
    if (editor) {
        editor.setMode(mode);
    }
}

// 显示验证错误
function showValidationErrors(editorId, errors) {
    const validationId = '{{ validation_id }}';
    const resultDiv = document.getElementById(validationId);
    if (!resultDiv) return;
    
    const errorMessages = errors.map(error => error.message || error.toString()).join('<br>');
    resultDiv.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>JSON验证失败:</strong><br>
            ${errorMessages}
        </div>
    `;
    resultDiv.style.display = 'block';
}

// 显示验证成功
function showValidationSuccess(editorId) {
    const validationId = '{{ validation_id }}';
    const resultDiv = document.getElementById(validationId);
    if (!resultDiv) return;
    
    resultDiv.innerHTML = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            JSON验证通过
        </div>
    `;
    resultDiv.style.display = 'block';
    
    // 3秒后自动隐藏成功消息
    setTimeout(() => {
        hideValidationErrors(editorId);
    }, 3000);
}

// 隐藏验证结果
function hideValidationErrors(editorId) {
    const validationId = '{{ validation_id }}';
    const resultDiv = document.getElementById(validationId);
    if (resultDiv) {
        resultDiv.style.display = 'none';
    }
}

// 通知函数（如果不存在则创建简单版本）
function showNotification(message, type = 'info') {
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
    } else {
        // 简单的通知实现
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
}

// 页面加载完成后初始化编辑器
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保库已加载
    setTimeout(() => {
        if (typeof createJSONEditor === 'function') {
            initJSONEditor('{{ editor_id }}');
        } else {
            console.error('JSON Editor library not loaded');
        }
    }, 100);
});
</script>
